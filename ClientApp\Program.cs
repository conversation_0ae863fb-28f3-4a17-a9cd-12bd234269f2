using System;
using System.IO;
using System.Net.Sockets;
using System.Windows.Forms;
using System.Threading;
using System.Threading.Tasks;
using SecureWinSuite.Shared.Networking;
using SecureWinSuite.ClientApp.Configuration;
using SecureWinSuite.Shared.Security;
using SecureWinSuite.ClientApp.Security;
using SecureWinSuite.ClientApp.UI;
using SecureWinSuite.Shared.Logging;

namespace SecureWinSuite.ClientApp
{
    internal static class Program
    {
        [STAThread]
        private static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Initialize logging to AppData to avoid permission issues
            try
            {
                var logDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SecureWinSuite", "logs");
                Logger.Init(logDir);
                Logger.Info("Startup: Client initializing.");
            }
            catch { }

            // Early blacklist check: do not show UI if HWID is blacklisted.
            try
            {
                if (EarlyBlacklistCheck())
                {
                    // Silent terminate before any UI loads
                    try { Application.Exit(); } catch { }
                    try { Environment.Exit(0); } catch { }
                    return;
                }
            }
            catch { /* fail open: if check fails, continue to UI */ }

            Application.Run(new UI.MainForm());
        }

        private static bool EarlyBlacklistCheck()
        {
            try
            {
                var hwid = HardwareFingerprint.GetHwid();
                if (string.IsNullOrWhiteSpace(hwid)) return false; // cannot determine

                using (var cts = new CancellationTokenSource(ClientConfig.BlacklistCheckTimeout))
                using (var tcp = new TcpClient { NoDelay = true })
                {
                    // Connect with timeout
                    var connectTask = tcp.ConnectAsync(ClientConfig.Host, ClientConfig.Port);
                    var completed = Task.WhenAny(connectTask, Task.Delay(ClientConfig.BlacklistCheckTimeout, cts.Token)).GetAwaiter().GetResult();
                    if (completed != connectTask || connectTask.IsFaulted)
                    {
                        // Server unreachable: consult local cache
                        if (BlacklistCache.TryGetBlacklisted(hwid, out var cachedBlacklisted) && cachedBlacklisted)
                        {
                            Logger.Info("Startup: server unreachable; cached blacklist hit -> exit.");
                            // Feedback (offline, cached)
                            try
                            {
                                AutoCloseMessageBox.Show(
                                    "Access denied: device is blacklisted (offline cache).", "Security", ClientConfig.BlacklistNoticeSeconds, true);
                            }
                            catch { }
                            try { Application.Exit(); } catch { }
                            try { Environment.Exit(0); } catch { }
                            return true;
                        }
                        else
                        {
                            // Optional feedback for offline allowed path
                            Logger.Info("Startup: server unreachable; no cache -> continue offline.");
                            try { if (ClientConfig.ShowOfflineNotice) AutoCloseMessageBox.Show("Server unreachable. Continuing in offline mode.", "Security", ClientConfig.OfflineNoticeSeconds, true); } catch { }
                            return false; // allow app to start
                        }
                    }

                    using (var stream = tcp.GetStream())
                    {
                        var protocol = new AesTcpProtocol(ClientConfig.Passphrase, ClientConfig.Salt, ClientConfig.Iterations);
                        var req = new BlacklistCheckRequest { Hwid = hwid };
                        protocol.SendAsync(stream, PacketType.BlacklistCheckRequest, req, cts.Token).GetAwaiter().GetResult();

                        var packet = protocol.ReceiveAsync(stream, cts.Token).GetAwaiter().GetResult();
                        if (packet != null && packet.Type == PacketType.BlacklistCheckResponse)
                        {
                            var resp = packet.GetPayload<BlacklistCheckResponse>();
                            if (resp != null && resp.Blacklisted)
                            {
                                // Persist locally for offline enforcement
                                try { BlacklistCache.SaveBlacklisted(hwid); } catch { }
                                // Feedback and exit immediately
                                Logger.Info("Startup: server reachable; device blacklisted -> cache+exit.");
                                try { AutoCloseMessageBox.Show("Access denied: device is blacklisted.", "Security", ClientConfig.BlacklistNoticeSeconds, true); } catch { }
                                try { Application.Exit(); } catch { }
                                try { Environment.Exit(0); } catch { }
                                return true; // blacklisted: exit
                            }
                            else
                            {
                                // Not blacklisted per server: clean any stale local entry
                                try { BlacklistCache.ClearBlacklisted(hwid); } catch { }
                                Logger.Info("Startup: server reachable; device NOT blacklisted -> continue.");
                            }
                        }
                    }
                }
            }
            catch
            {
                // network/protocol errors: consult cache, otherwise allow
                try
                {
                    var hwid = HardwareFingerprint.GetHwid();
                    if (!string.IsNullOrWhiteSpace(hwid) && BlacklistCache.TryGetBlacklisted(hwid, out var cachedBlacklisted) && cachedBlacklisted)
                    {
                        Logger.Info("Startup: exception during check; cached blacklist hit -> exit.");
                        try { AutoCloseMessageBox.Show("Access denied: device is blacklisted (offline cache).", "Security", ClientConfig.BlacklistNoticeSeconds, true); } catch { }
                        try { Application.Exit(); } catch { }
                        try { Environment.Exit(0); } catch { }
                        return true;
                    }
                }
                catch (Exception ex) { try { Logger.Error("Startup: exception in fallback cache check: " + ex.Message); } catch { } }
            }
            return false;
        }
    }
}
