using System;
using System.Net.Sockets;
using System.Windows.Forms;
using System.Threading;
using System.Threading.Tasks;
using SecureWinSuite.Shared.Networking;
using SecureWinSuite.ClientApp.Configuration;
using SecureWinSuite.Shared.Security;

namespace SecureWinSuite.ClientApp
{
    internal static class Program
    {
        [STAThread]
        private static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Early blacklist check: do not show UI if HWID is blacklisted.
            try
            {
                if (EarlyBlacklistCheck())
                {
                    // Silent terminate before any UI loads
                    try { Application.Exit(); } catch { }
                    try { Environment.Exit(0); } catch { }
                    return;
                }
            }
            catch { /* fail open: if check fails, continue to UI */ }

            Application.Run(new UI.MainForm());
        }

        private static bool EarlyBlacklistCheck()
        {
            try
            {
                var hwid = HardwareFingerprint.GetHwid();
                if (string.IsNullOrWhiteSpace(hwid)) return false; // cannot determine

                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(2.5)))
                using (var tcp = new TcpClient { NoDelay = true })
                {
                    // Connect with timeout
                    var connectTask = tcp.ConnectAsync(ClientConfig.Host, ClientConfig.Port);
                    var completed = Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(2.5), cts.Token)).GetAwaiter().GetResult();
                    if (completed != connectTask || connectTask.IsFaulted)
                    {
                        return false; // server unreachable; allow app to start
                    }

                    using (var stream = tcp.GetStream())
                    {
                        var protocol = new AesTcpProtocol(ClientConfig.Passphrase, ClientConfig.Salt, ClientConfig.Iterations);
                        var req = new BlacklistCheckRequest { Hwid = hwid };
                        protocol.SendAsync(stream, PacketType.BlacklistCheckRequest, req, cts.Token).GetAwaiter().GetResult();

                        var packet = protocol.ReceiveAsync(stream, cts.Token).GetAwaiter().GetResult();
                        if (packet != null && packet.Type == PacketType.BlacklistCheckResponse)
                        {
                            var resp = packet.GetPayload<BlacklistCheckResponse>();
                            if (resp != null && resp.Blacklisted)
                            {
                                return true; // blacklisted: exit
                            }
                        }
                    }
                }
            }
            catch
            {
                // network/protocol errors -> do not block startup
            }
            return false;
        }
    }
}
