using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;

namespace SecureWinSuite.ClientApp.Security
{
    public static class AntiDebug
    {
        [DllImport("kernel32.dll")]
        private static extern bool IsDebuggerPresent();

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CheckRemoteDebuggerPresent(IntPtr hProcess, ref bool isDebuggerPresent);

        private static readonly string[] SuspiciousProcessNames = new[]
        {
            "x64dbg", "x32dbg", "ida", "ida64", "ollydbg", "cheatengine", "scylla", "procmon", "procexp",
            "wireshark", "fiddler", "dnspy", "ilspy"
        };

        public static bool IsAnyDebuggerAttached()
        {
            if (Debugger.IsAttached) return true;
            try { if (IsDebuggerPresent()) return true; } catch { }
            try { bool present = false; if (CheckRemoteDebuggerPresent(Process.GetCurrentProcess().Handle, ref present) && present) return true; } catch { }
            return false;
        }

        public static string[] FindSuspiciousProcesses()
        {
            var procs = new List<string>();
            try
            {
                foreach (var name in SuspiciousProcessNames)
                {
                    try
                    {
                        if (Process.GetProcessesByName(name).Any()) procs.Add(name);
                    }
                    catch (Win32Exception) { }
                    catch { }
                }
            }
            catch { }
            return procs.ToArray();
        }
    }
}
