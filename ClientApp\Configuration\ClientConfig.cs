using System;
using System.Configuration;
using System.Globalization;

namespace SecureWinSuite.ClientApp.Configuration
{
    public static class ClientConfig
    {
        public static string Host => ConfigurationManager.AppSettings["ServerHost"] ?? "127.0.0.1";
        public static int Port => int.TryParse(ConfigurationManager.AppSettings["ServerPort"], out var p) ? p : 5055;
        public static string Passphrase => ConfigurationManager.AppSettings["CryptoPassphrase"] ?? "ChangeThisPassphrase";
        public static byte[] Salt => Convert.FromBase64String(ConfigurationManager.AppSettings["CryptoSaltBase64"] ?? "bXlTdXBlclNhbHQxMjM0NQ==");
        public static int Iterations => int.TryParse(ConfigurationManager.AppSettings["CryptoIterations"], out var i) ? i : 100_000;
        public static TimeSpan HeartbeatInterval => TimeSpan.FromSeconds(20);

        // Blacklist/Startup settings
        public static TimeSpan BlacklistCheckTimeout
        {
            get
            {
                var val = ConfigurationManager.AppSettings["BlacklistCheckTimeoutSeconds"];
                if (double.TryParse(val, NumberStyles.Float, CultureInfo.InvariantCulture, out var s) && s > 0)
                    return TimeSpan.FromSeconds(s);
                return TimeSpan.FromSeconds(2.5);
            }
        }

        public static bool ShowOfflineNotice
        {
            get
            {
                var val = ConfigurationManager.AppSettings["ShowOfflineNotice"];
                return bool.TryParse(val, out var b) ? b : true;
            }
        }

        public static int OfflineNoticeSeconds
        {
            get
            {
                var val = ConfigurationManager.AppSettings["OfflineNoticeSeconds"];
                return int.TryParse(val, out var s) && s > 0 ? s : 2;
            }
        }

        public static int BlacklistNoticeSeconds
        {
            get
            {
                var val = ConfigurationManager.AppSettings["BlacklistNoticeSeconds"];
                return int.TryParse(val, out var s) && s > 0 ? s : 2;
            }
        }
    }
}
