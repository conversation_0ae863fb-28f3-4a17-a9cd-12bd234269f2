using System;
using System.Configuration;

namespace SecureWinSuite.ClientApp.Configuration
{
    public static class ClientConfig
    {
        public static string Host => ConfigurationManager.AppSettings["ServerHost"] ?? "127.0.0.1";
        public static int Port => int.TryParse(ConfigurationManager.AppSettings["ServerPort"], out var p) ? p : 5055;
        public static string Passphrase => ConfigurationManager.AppSettings["CryptoPassphrase"] ?? "ChangeThisPassphrase";
        public static byte[] Salt => Convert.FromBase64String(ConfigurationManager.AppSettings["CryptoSaltBase64"] ?? "bXlTdXBlclNhbHQxMjM0NQ==");
        public static int Iterations => int.TryParse(ConfigurationManager.AppSettings["CryptoIterations"], out var i) ? i : 100_000;
        public static TimeSpan HeartbeatInterval => TimeSpan.FromSeconds(20);
    }
}
