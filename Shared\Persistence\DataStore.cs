using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using Newtonsoft.Json;
using SecureWinSuite.Shared.Models;

namespace SecureWinSuite.Shared.Persistence
{
    public class DataStore
    {
        public List<SerialKey> SerialKeys { get; set; } = new List<SerialKey>();
        public List<Incident> Incidents { get; set; } = new List<Incident>();
        public HashSet<string> BlacklistedHwids { get; set; } = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
    }

    public class JsonRepository
    {
        private readonly string _directory;
        private readonly string _dataFilePath;
        private readonly ReaderWriterLockSlim _lock = new ReaderWriterLockSlim();

        public JsonRepository(string directory)
        {
            _directory = directory ?? throw new ArgumentNullException(nameof(directory));
            Directory.CreateDirectory(_directory);
            _dataFilePath = Path.Combine(_directory, "database.json");
            if (!File.Exists(_dataFilePath))
            {
                Save(new DataStore());
            }
        }

        public DataStore Load()
        {
            _lock.EnterReadLock();
            try
            {
                var json = File.ReadAllText(_dataFilePath);
                var store = JsonConvert.DeserializeObject<DataStore>(json) ?? new DataStore();
                // normalize
                store.SerialKeys = store.SerialKeys ?? new List<SerialKey>();
                store.Incidents = store.Incidents ?? new List<Incident>();
                store.BlacklistedHwids = store.BlacklistedHwids ?? new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                return store;
            }
            finally { _lock.ExitReadLock(); }
        }

        public void Save(DataStore store)
        {
            _lock.EnterWriteLock();
            try
            {
                var json = JsonConvert.SerializeObject(store, Formatting.Indented);
                File.WriteAllText(_dataFilePath, json);
            }
            finally { _lock.ExitWriteLock(); }
        }

        public T Update<T>(Func<DataStore, T> update)
        {
            _lock.EnterWriteLock();
            try
            {
                var json = File.ReadAllText(_dataFilePath);
                var store = JsonConvert.DeserializeObject<DataStore>(json) ?? new DataStore();
                var result = update(store);
                var outJson = JsonConvert.SerializeObject(store, Formatting.Indented);
                File.WriteAllText(_dataFilePath, outJson);
                return result;
            }
            finally { _lock.ExitWriteLock(); }
        }

        // Convenience helpers
        public SerialKey? FindKey(string key, string productId)
        {
            var store = Load();
            return store.SerialKeys.FirstOrDefault(k => string.Equals(k.Key, key, StringComparison.OrdinalIgnoreCase)
                                                    && string.Equals(k.ProductId, productId, StringComparison.OrdinalIgnoreCase));
        }
    }
}
