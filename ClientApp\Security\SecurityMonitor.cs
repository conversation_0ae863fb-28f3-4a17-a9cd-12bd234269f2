using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using SecureWinSuite.ClientApp.Networking;
using SecureWinSuite.Shared.Networking;
using System.Runtime.InteropServices;

namespace SecureWinSuite.ClientApp.Security
{
    public class SecurityMonitor
    {
        private CancellationTokenSource? _cts;
        private ClientConnection? _conn;
        private string _serialKey = string.Empty;
        private string _productId = string.Empty;
        private string _hwid = string.Empty;

        // Simple self-hash check; in real scenarios use server-side validation and code signing
        private static string ComputeSelfHash()
        {
            try
            {
                var exe = Assembly.GetExecutingAssembly().Location;
                using var sha = SHA256.Create();
                using var fs = File.OpenRead(exe);
                var hash = sha.ComputeHash(fs);
                return BitConverter.ToString(hash).Replace("-", string.Empty);
            }
            catch { return string.Empty; }
        }

        private static bool IsTampered(string? expectedHash)
        {
            if (string.IsNullOrWhiteSpace(expectedHash)) return false; // disabled if no baseline
            var actual = ComputeSelfHash();
            return !string.Equals(expectedHash, actual, StringComparison.OrdinalIgnoreCase);
        }

        public bool CheckPreLogin(out ViolationReport report)
        {
            report = new ViolationReport
            {
                TimestampUtc = DateTime.UtcNow,
                ViolationType = string.Empty,
                Message = string.Empty,
                PostLogin = false
            };

            if (AntiDebug.IsAnyDebuggerAttached())
            {
                report.ViolationType = "DEBUGGER_DETECTED";
                report.Message = "Debugger detected before login";
                return true;
            }

            var procs = AntiDebug.FindSuspiciousProcesses();
            if (procs.Length > 0)
            {
                report.ViolationType = "SUSPICIOUS_PROCESS";
                report.Message = "Suspicious tools running: " + string.Join(",", procs);
                return true;
            }

            report = null!;
            return false;
        }

        public void StartPostLoginMonitoring(ClientConnection conn, string serialKey, string productId, string hwid, string? expectedHash = null)
        {
            _conn = conn;
            _serialKey = serialKey;
            _productId = productId;
            _hwid = hwid;
            _cts = new CancellationTokenSource();
            var ct = _cts.Token;
            _ = Task.Run(async () =>
            {
                while (!ct.IsCancellationRequested)
                {
                    try
                    {
                        if (AntiDebug.IsAnyDebuggerAttached())
                        {
                            await ReportAndExitAsync("DEBUGGER_DETECTED", "Debugger attached after login").ConfigureAwait(false);
                            break;
                        }
                        var procs = AntiDebug.FindSuspiciousProcesses();
                        if (procs.Length > 0)
                        {
                            await ReportAndExitAsync("SUSPICIOUS_PROCESS", "Suspicious tools running: " + string.Join(",", procs)).ConfigureAwait(false);
                            break;
                        }
                        if (IsTampered(expectedHash))
                        {
                            await ReportAndExitAsync("TAMPER_DETECTED", "Executable integrity check failed").ConfigureAwait(false);
                            break;
                        }
                    }
                    catch { }
                    await Task.Delay(TimeSpan.FromSeconds(5), ct).ConfigureAwait(false);
                }
            }, ct);
        }

        [DllImport("user32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern int MessageBoxTimeout(IntPtr hWnd, string text, string caption, uint type, short wLanguageId, int milliseconds);

        private static void ShowAutoClosingMessageBox(string text, string caption, int seconds, bool errorIcon)
        {
            const uint MB_OK = 0x00000000;
            const uint MB_ICONERROR = 0x00000010;
            const uint MB_ICONINFORMATION = 0x00000040;
            uint flags = MB_OK | (errorIcon ? MB_ICONERROR : MB_ICONINFORMATION);
            try { MessageBoxTimeout(IntPtr.Zero, text, caption, flags, 0, seconds * 1000); } catch { }
        }

        private async Task ReportAndExitAsync(string type, string message)
        {
            try
            {
                var report = new ViolationReport
                {
                    TimestampUtc = DateTime.UtcNow,
                    ViolationType = type,
                    Message = message,
                    SerialKey = _serialKey,
                    ProductId = _productId,
                    Hwid = _hwid,
                    PostLogin = true
                };
                if (_conn != null)
                {
                    await _conn.ReportViolationAsync(report).ConfigureAwait(false);
                }
            }
            catch { }

#if DEBUG
            // In DEBUG, also exit after showing an auto-closing notice (per user request).
            try { if (_conn != null) _conn.SuppressDisconnectNotification = true; } catch { }
            try { _cts?.Cancel(); } catch { }
            try { _conn?.Dispose(); } catch { }
            ShowAutoClosingMessageBox(
                "Security violation detected. Your session will be terminated. This window will close in 5 seconds.",
                "Security",
                5,
                true);
            try { System.Windows.Forms.Application.Exit(); } catch { }
            Environment.Exit(0);
            return;
#else
            try { if (_conn != null) _conn.SuppressDisconnectNotification = true; } catch { }
            try { _cts?.Cancel(); } catch { }
            try { _conn?.Dispose(); } catch { }
            ShowAutoClosingMessageBox(
                "Security violation detected. Your session will be terminated. This window will close in 5 seconds.",
                "Security",
                5,
                true);
            Environment.Exit(0);
#endif
        }
    }
}
