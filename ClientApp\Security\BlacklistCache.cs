using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace SecureWinSuite.ClientApp.Security
{
    internal static class BlacklistCache
    {
        // Hidden persisted cache of blacklisted HWID hashes for offline enforcement
        private static readonly byte[] Entropy = Encoding.UTF8.GetBytes("SWS_BL_CACHE_v1");

        private static string GetDir()
        {
            var dir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SecureWinSuite", ".sys");
            try
            {
                if (!Directory.Exists(dir)) Directory.CreateDirectory(dir);
                // Make directory hidden if possible
                var di = new DirectoryInfo(dir);
                if ((di.Attributes & FileAttributes.Hidden) == 0)
                {
                    di.Attributes |= FileAttributes.Hidden;
                }
            }
            catch { }
            return dir;
        }

        private static string GetPath() => Path.Combine(GetDir(), ".blcache");

        private static string HashHwid(string hwid)
        {
            using (var sha = SHA256.Create())
            {
                var bytes = sha.ComputeHash(Encoding.UTF8.GetBytes(hwid ?? string.Empty));
                return BitConverter.ToString(bytes).Replace("-", string.Empty);
            }
        }

        public static bool TryGetBlacklisted(string hwid, out bool blacklisted)
        {
            blacklisted = false;
            try
            {
                var path = GetPath();
                if (!File.Exists(path)) return false;
                var enc = File.ReadAllBytes(path);
                var dec = ProtectedData.Unprotect(enc, Entropy, DataProtectionScope.CurrentUser);
                var content = Encoding.UTF8.GetString(dec);
                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                var hash = HashHwid(hwid);
                blacklisted = lines.Contains(hash, StringComparer.OrdinalIgnoreCase);
                return true; // we had local data
            }
            catch
            {
                return false;
            }
        }

        public static void SaveBlacklisted(string hwid)
        {
            try
            {
                var path = GetPath();
                var hash = HashHwid(hwid);
                var set = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                if (File.Exists(path))
                {
                    try
                    {
                        var enc = File.ReadAllBytes(path);
                        var dec = ProtectedData.Unprotect(enc, Entropy, DataProtectionScope.CurrentUser);
                        var content = Encoding.UTF8.GetString(dec);
                        foreach (var line in content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries))
                            set.Add(line);
                    }
                    catch { }
                }
                set.Add(hash);
                var joined = string.Join("\n", set);
                var data = Encoding.UTF8.GetBytes(joined);
                var encOut = ProtectedData.Protect(data, Entropy, DataProtectionScope.CurrentUser);
                File.WriteAllBytes(path, encOut);
                try
                {
                    var fi = new FileInfo(path);
                    if ((fi.Attributes & FileAttributes.Hidden) == 0)
                    {
                        fi.Attributes |= FileAttributes.Hidden;
                    }
                }
                catch { }
            }
            catch { }
        }

        public static void ClearBlacklisted(string hwid)
        {
            try
            {
                var path = GetPath();
                if (!File.Exists(path)) return;
                var enc = File.ReadAllBytes(path);
                var dec = ProtectedData.Unprotect(enc, Entropy, DataProtectionScope.CurrentUser);
                var content = Encoding.UTF8.GetString(dec);
                var list = new List<string>(content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries));
                var hash = HashHwid(hwid);
                var removed = list.RemoveAll(s => string.Equals(s, hash, StringComparison.OrdinalIgnoreCase));
                if (removed > 0)
                {
                    if (list.Count == 0)
                    {
                        File.Delete(path);
                    }
                    else
                    {
                        var joined = string.Join("\n", list);
                        var data = Encoding.UTF8.GetBytes(joined);
                        var encOut = ProtectedData.Protect(data, Entropy, DataProtectionScope.CurrentUser);
                        File.WriteAllBytes(path, encOut);
                    }
                }
            }
            catch { }
        }
    }
}
