using System;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using SecureWinSuite.ClientApp.Networking;
using SecureWinSuite.ClientApp.Security;
using SecureWinSuite.Shared.Logging;
using SecureWinSuite.Shared.Networking;
using SecureWinSuite.Shared.Security;
using SecureWinSuite.ClientApp.UI;

namespace SecureWinSuite.ClientApp.UI
{
    public partial class MainForm : Form
    {
        private ClientConnection? _conn;
        private string _hwid = string.Empty;
        private readonly SecurityMonitor _security = new SecurityMonitor();
        private CancellationTokenSource _cts = new CancellationTokenSource();
        private string _productId = string.Empty;

        public MainForm()
        {
            InitializeComponent();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            Logger.Init();
            try { _hwid = HardwareFingerprint.GetHwid(); }
            catch { _hwid = Guid.NewGuid().ToString("N"); }
            txtLog.AppendText($"HWID: {_hwid}{Environment.NewLine}");
            try { txtHwidDisplay.Text = _hwid; } catch { }
            try { lblStatus.Text = "Ready"; } catch { }

            // Pre-login detection: in DEBUG, log and continue; in RELEASE, report then exit silently
#if DEBUG
            if (_security.CheckPreLogin(out var report))
            {
                txtLog.AppendText("Pre-login security violation detected. Exiting...\r\n");
                _ = Task.Run(async () =>
                {
                    try
                    {
                        using var c = new ClientConnection { SuppressDisconnectNotification = true };
                        await c.ConnectAsync(_cts.Token);
                        report.Hwid = _hwid;
                        await c.ReportViolationAsync(report);
                    }
                    catch { }
                });
                AutoCloseMessageBox.Show(
                    "Security violation detected. The application will exit in 5 seconds.",
                    "Security",
                    5,
                    true);
                try { Close(); } catch { }
                try { Application.Exit(); } catch { }
                try { Environment.Exit(0); } catch { }
                return;
            }
#else
            if (_security.CheckPreLogin(out var report))
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        using var c = new ClientConnection { SuppressDisconnectNotification = true };
                        await c.ConnectAsync(_cts.Token);
                        report.Hwid = _hwid;
                        await c.ReportViolationAsync(report);
                    }
                    catch { }
                });
                AutoCloseMessageBox.Show(
                    "Security violation detected. The application will exit in 5 seconds.",
                    "Security",
                    5,
                    true);
                try { Application.Exit(); } catch { }
                try { Environment.Exit(0); } catch { }
                return;
            }
#endif
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            var serial = txtSerial.Text.Trim();
            if (string.IsNullOrEmpty(serial))
            {
                MessageBox.Show("Enter a license key.", "Missing info", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!TryExtractProductId(serial, out _productId))
            {
                MessageBox.Show("Invalid license key format.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            btnLogin.Enabled = false;
            try { lblStatus.Text = "Authenticating..."; } catch { }
            try
            {
                _conn = new ClientConnection();
                _conn.LoginReceived += OnLoginReceived;
                _conn.Disconnected += OnDisconnected;
                await _conn.ConnectAsync(_cts.Token);

                var req = new LoginRequest
                {
                    SerialKey = serial,
                    ProductId = _productId,
                    Hwid = _hwid,
                    ClientVersion = Application.ProductVersion
                };
                await _conn.SendLoginAsync(req, _cts.Token);
                AppendLog("Login request sent.");
            }
            catch (Exception ex)
            {
                AppendLog($"Login failed: {ex.Message}");
                btnLogin.Enabled = true;
                try { lblStatus.Text = "Ready"; } catch { }
            }
        }

        private void OnLoginReceived(object? sender, LoginResponse e)
        {
            if (InvokeRequired)
            {
                BeginInvoke(new Action<object?, LoginResponse>(OnLoginReceived), sender, e);
                return;
            }

            AppendLog($"Login response: {(e.Success ? "Success" : "Failed")} - {e.Message}");

            if (e.Success)
            {
                btnLogout.Enabled = true;
                _conn?.StartHeartbeat();
                _security.StartPostLoginMonitoring(_conn!, txtSerial.Text.Trim(), _productId, _hwid);
                try { lblStatus.Text = "Connected"; } catch { }
            }
            else
            {
                if (e.Locked)
                {
                    MessageBox.Show("Account is locked.", "Access denied", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                btnLogin.Enabled = true;
                try { lblStatus.Text = "Ready"; } catch { }
            }
        }

        private void OnDisconnected(object? sender, string e)
        {
            if (InvokeRequired)
            {
                BeginInvoke(new Action<object?, string>(OnDisconnected), sender, e);
                return;
            }
            AppendLog($"Disconnected: {e}");
            btnLogin.Enabled = true;
            btnLogout.Enabled = false;
            try { lblStatus.Text = "Disconnected"; } catch { }
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            try { if (_conn != null) _conn.SuppressDisconnectNotification = true; } catch { }
            try { _conn?.Dispose(); } catch { }
            btnLogin.Enabled = true;
            btnLogout.Enabled = false;
            try { lblStatus.Text = "Ready"; } catch { }
        }

        private void AppendLog(string msg)
        {
            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] {msg}{Environment.NewLine}");
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            base.OnClosing(e);
            try { _cts.Cancel(); } catch { }
            try { if (_conn != null) _conn.SuppressDisconnectNotification = true; } catch { }
            try { _conn?.Dispose(); } catch { }
        }

        private static bool TryExtractProductId(string key, out string productId)
        {
            productId = string.Empty;
            try
            {
                var idx = key.IndexOf('-');
                if (idx <= 0) return false;
                productId = key.Substring(0, idx).Trim();
                return !string.IsNullOrWhiteSpace(productId);
            }
            catch
            {
                return false;
            }
        }

        private void btnPasteKey_Click(object sender, EventArgs e)
        {
            try
            {
                if (Clipboard.ContainsText())
                {
                    var text = Clipboard.GetText()?.Trim();
                    if (!string.IsNullOrEmpty(text))
                    {
                        txtSerial.Text = text;
                        txtSerial.SelectAll();
                        txtSerial.Focus();
                    }
                }
            }
            catch { }
        }

        private void btnCopyHwid_Click(object sender, EventArgs e)
        {
            try { Clipboard.SetText(_hwid); }
            catch { }
        }
    }
}
