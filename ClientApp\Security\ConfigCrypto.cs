using System;
using System.Security.Cryptography;
using System.Text;

namespace SecureWinSuite.ClientApp.Security
{
    internal static class ConfigCrypto
    {
        private static readonly byte[] Entropy = Encoding.UTF8.GetBytes("SWS_CFG_v1");

        public static byte[] Encrypt(byte[] plain)
        {
            try { return ProtectedData.Protect(plain, Entropy, DataProtectionScope.CurrentUser); }
            catch { return Array.Empty<byte>(); }
        }

        public static byte[] Decrypt(byte[] cipher)
        {
            try { return ProtectedData.Unprotect(cipher, Entropy, DataProtectionScope.CurrentUser); }
            catch { return Array.Empty<byte>(); }
        }
    }
}
