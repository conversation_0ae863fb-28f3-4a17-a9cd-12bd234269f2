using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Newtonsoft.Json;
using SecureWinSuite.ClientApp.Security;

namespace SecureWinSuite.ClientApp.Configuration
{
    internal class ClientConfigData
    {
        public string SerialKey { get; set; } = string.Empty;
        public bool AutoLogin { get; set; } = false;
        public Dictionary<string, string> Settings { get; set; } = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
    }

    internal static class ConfigManager
    {
        private static string GetPath()
        {
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(baseDir, "config.dat");
        }

        public static ClientConfigData Load()
        {
            try
            {
                var path = GetPath();
                if (!File.Exists(path)) return new ClientConfigData();
                var enc = File.ReadAllBytes(path);
                var dec = ConfigCrypto.Decrypt(enc);
                if (dec == null || dec.Length == 0) return new ClientConfigData();
                var json = Encoding.UTF8.GetString(dec);
                var data = JsonConvert.DeserializeObject<ClientConfigData>(json) ?? new ClientConfigData();
                return data;
            }
            catch
            {
                return new ClientConfigData();
            }
        }

        public static void Save(ClientConfigData data)
        {
            try
            {
                var json = JsonConvert.SerializeObject(data);
                var bytes = Encoding.UTF8.GetBytes(json);
                var enc = ConfigCrypto.Encrypt(bytes);
                var path = GetPath();
                File.WriteAllBytes(path, enc);
                try { var fi = new FileInfo(path); fi.Attributes |= FileAttributes.Hidden; } catch { }
            }
            catch { }
        }

        public static void UpdateSerialAutoLogin(string serial, bool autoLogin)
        {
            try
            {
                var cfg = Load();
                cfg.SerialKey = serial ?? string.Empty;
                cfg.AutoLogin = autoLogin;
                Save(cfg);
            }
            catch { }
        }
    }
}
