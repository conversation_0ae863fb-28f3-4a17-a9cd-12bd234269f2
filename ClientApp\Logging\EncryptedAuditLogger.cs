using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;

namespace SecureWinSuite.ClientApp.Logging
{
    // Encrypted audit logger: writes JSON events to log.dat using RSA (header) + AES-CBC + HMAC per entry.
    internal static class Audit
    {
        private static readonly object Sync = new object();
        private static FileStream? _fs;
        private static byte[]? _aesKey;
        private static byte[]? _hmacKey; // derived from AES key

        // Replace with your server's RSA public key (XML). Server/Admin GUI holds the private key to decrypt.
        // NOTE: This is a sample 2048-bit key. Replace in production.
        private const string PublicKeyXml =
            "<RSAKeyValue><Modulus>v1h5a6Jf2u1Q2rJ3kA2+g8q6cQ/2/0sVj7b5H7s4bqI2YlYFvJqN5bF8h0r3Y9k0S3q5b5GQ2r2VJj1y0fG9qT1y5nE4xA9Gv8kZJv7iY0Lkq8s8fL7yofb7S2Yb7n3Xv4nFq3puJH3b5mJkYm0r1u3v2o3t4u5v6w7x8y9z0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>";

        public static void Init()
        {
            try
            {
                lock (Sync)
                {
                    var baseDir = AppDomain.CurrentDomain.BaseDirectory;
                    var path = Path.Combine(baseDir, "log.dat");
                    try { if (File.Exists(path)) File.Delete(path); } catch { }
                    _fs = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.Read);
                    try
                    {
                        var fi = new FileInfo(path);
                        fi.Attributes |= FileAttributes.Hidden;
                    }
                    catch { }

                    // Generate session AES key
                    using (var rng = RandomNumberGenerator.Create())
                    {
                        _aesKey = new byte[32];
                        rng.GetBytes(_aesKey);
                        using (var sha = SHA256.Create())
                        {
                            // derive HMAC key = SHA256(aesKey || 0x01)
                            var buf = new byte[_aesKey.Length + 1];
                            Buffer.BlockCopy(_aesKey, 0, buf, 0, _aesKey.Length);
                            buf[buf.Length - 1] = 0x01;
                            _hmacKey = sha.ComputeHash(buf);
                        }
                    }

                    // Encrypt AES key with RSA public key and write header
                    var encKey = RsaEncrypt(_aesKey);
                    var magic = Encoding.ASCII.GetBytes("SWSLOG1\n");
                    _fs.Write(magic, 0, magic.Length);
                    var len = BitConverter.GetBytes((ushort)encKey.Length);
                    _fs.Write(len, 0, len.Length);
                    _fs.Write(encKey, 0, encKey.Length);
                    _fs.Flush();
                }
            }
            catch { /* swallow */ }
        }

        private static byte[] RsaEncrypt(byte[] data)
        {
            using (var rsa = new RSACryptoServiceProvider(2048))
            {
                rsa.PersistKeyInCsp = false;
                rsa.FromXmlString(PublicKeyXml);
                return rsa.Encrypt(data, true); // OAEP
            }
        }

        private static void Write(string level, string message)
        {
            try
            {
                lock (Sync)
                {
                    if (_fs == null || _aesKey == null || _hmacKey == null) return;

                    var record = new { ts = DateTime.UtcNow.ToString("O"), level, message };
                    var json = JsonConvert.SerializeObject(record);
                    var plain = Encoding.UTF8.GetBytes(json);

                    // Encrypt with AES-CBC using per-entry IV
                    byte[] iv = new byte[16];
                    using (var rng = RandomNumberGenerator.Create()) rng.GetBytes(iv);

                    byte[] cipher;
                    using (var aes = Aes.Create())
                    {
                        aes.Mode = CipherMode.CBC;
                        aes.Padding = PaddingMode.PKCS7;
                        aes.Key = _aesKey;
                        aes.IV = iv;
                        using (var enc = aes.CreateEncryptor())
                        {
                            cipher = enc.TransformFinalBlock(plain, 0, plain.Length);
                        }
                    }

                    // HMAC over iv||cipher
                    byte[] hmac;
                    using (var h = new HMACSHA256(_hmacKey))
                    {
                        var macInput = new byte[iv.Length + cipher.Length];
                        Buffer.BlockCopy(iv, 0, macInput, 0, iv.Length);
                        Buffer.BlockCopy(cipher, 0, macInput, iv.Length, cipher.Length);
                        hmac = h.ComputeHash(macInput);
                    }

                    // Write entry: [len(uint32)] [iv(16)] [cipher] [hmac(32)]
                    var entryLen = iv.Length + cipher.Length + hmac.Length;
                    var lenBytes = BitConverter.GetBytes(entryLen);
                    _fs.Write(lenBytes, 0, lenBytes.Length);
                    _fs.Write(iv, 0, iv.Length);
                    _fs.Write(cipher, 0, cipher.Length);
                    _fs.Write(hmac, 0, hmac.Length);
                    _fs.Flush();
                }
            }
            catch { /* swallow */ }
        }

        public static void Info(string message) => Write("INFO", message);
        public static void Warn(string message) => Write("WARN", message);
        public static void Error(string message) => Write("ERROR", message);
    }
}
