using System;

namespace SecureWinSuite.Shared.Models
{
    public class Incident
    {
        public string Id { get; set; } = Guid.NewGuid().ToString("N");
        public DateTime TimestampUtc { get; set; } = DateTime.UtcNow;
        public string Severity { get; set; } = "High"; // High, Medium, Low
        public string ViolationType { get; set; } = string.Empty; // e.g., AntiDebug, AntiTamper
        public string Message { get; set; } = string.Empty;
        public string? SerialKey { get; set; }
        public string? ProductId { get; set; }
        public string? Hwid { get; set; }
        public string? ClientInfo { get; set; }
        public bool PostLogin { get; set; }
        public string? ContextJson { get; set; }
    }
}
