using System;
using System.Diagnostics;
using System.IO;

namespace SecureWinSuite.Shared.Logging
{
    public static class Logger
    {
        private static readonly object _sync = new object();
        private static string _logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
        private static string LogFilePath => Path.Combine(_logDir, $"log_{DateTime.UtcNow:yyyyMMdd}.txt");

        public static void Init(string? customDir = null)
        {
            if (!string.IsNullOrWhiteSpace(customDir))
                _logDir = customDir!;
            Directory.CreateDirectory(_logDir);
            Trace.Listeners.Clear();
            Trace.Listeners.Add(new TextWriterTraceListener(LogFilePath));
            Trace.AutoFlush = true;
        }

        public static void Info(string message)
        {
            Write("INFO", message);
        }

        public static void Warn(string message)
        {
            Write("WARN", message);
        }

        public static void Error(string message, Exception? ex = null)
        {
            Write("ERROR", ex == null ? message : message + "\n" + ex);
        }

        private static void Write(string level, string message)
        {
            lock (_sync)
            {
                Directory.CreateDirectory(_logDir);
                File.AppendAllText(LogFilePath, $"[{DateTime.UtcNow:O}] [{level}] {message}{Environment.NewLine}");
            }
        }
    }
}
