using System;
using System.IO;
using SecureWinSuite.ServerApp.Configuration;
using SecureWinSuite.Shared.Logging;
using SecureWinSuite.Shared.Models;
using SecureWinSuite.Shared.Persistence;

namespace SecureWinSuite.ServerApp.Services
{
    public class LicenseService
    {
        private readonly JsonRepository _repo;

        public LicenseService()
        {
            var dataDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ServerConfig.DataDirectory);
            Directory.CreateDirectory(dataDir);
            _repo = new JsonRepository(dataDir);
        }

        public (bool success, string message, DateTime? expiration, bool locked) Authenticate(string serialKey, string productId, string hwid)
        {
            var key = _repo.FindKey(serialKey, productId);
            if (key == null)
            {
                Logger.Warn($"Auth failed: key not found for product {productId}");
                return (false, "Invalid key", null, false);
            }

            if (key.Locked)
            {
                Logger.Warn($"Auth blocked: key is locked {serialKey}");
                return (false, "Account locked", key.ExpirationUtc, true);
            }

            if (key.IsExpired(DateTime.UtcNow))
            {
                Logger.Info($"Auth failed: key expired {serialKey}");
                return (false, "License expired", key.ExpirationUtc, false);
            }

            // HWID binding logic
            if (string.IsNullOrWhiteSpace(key.BoundHwid))
            {
                _repo.Update(store =>
                {
                    var k = store.SerialKeys.Find(k => string.Equals(k.Key, serialKey, StringComparison.OrdinalIgnoreCase)
                                                     && string.Equals(k.ProductId, productId, StringComparison.OrdinalIgnoreCase));
                    if (k != null)
                    {
                        k.BoundHwid = hwid;
                        k.LastUsedUtc = DateTime.UtcNow;
                    }
                    return 0;
                });
                Logger.Info($"HWID bound for key {serialKey}");
            }
            else if (!string.Equals(key.BoundHwid, hwid, StringComparison.OrdinalIgnoreCase))
            {
                var message = "HWID mismatch";
                Logger.Warn($"{message} for key {serialKey}");
                if (ServerConfig.LockOnMultipleHwid)
                {
                    _repo.Update(store =>
                    {
                        var k = store.SerialKeys.Find(k => string.Equals(k.Key, serialKey, StringComparison.OrdinalIgnoreCase)
                                                         && string.Equals(k.ProductId, productId, StringComparison.OrdinalIgnoreCase));
                        if (k != null) k.Locked = true;
                        store.Incidents.Add(new Incident
                        {
                            Severity = "High",
                            ViolationType = "HWID_MISMATCH",
                            Message = "Key used on different hardware - auto-locked",
                            SerialKey = serialKey,
                            ProductId = productId,
                            Hwid = hwid,
                            PostLogin = false
                        });
                        return 0;
                    });
                    return (false, "Account locked due to HWID violation", key.ExpirationUtc, true);
                }
                else
                {
                    return (false, message, key.ExpirationUtc, false);
                }
            }
            else
            {
                _repo.Update(store =>
                {
                    var k = store.SerialKeys.Find(k => string.Equals(k.Key, serialKey, StringComparison.OrdinalIgnoreCase)
                                                     && string.Equals(k.ProductId, productId, StringComparison.OrdinalIgnoreCase));
                    if (k != null) k.LastUsedUtc = DateTime.UtcNow;
                    return 0;
                });
            }

            return (true, "Login successful", key.ExpirationUtc, false);
        }

        public void RecordIncident(Incident incident)
        {
            _repo.Update(store =>
            {
                store.Incidents.Add(incident);
                return 0;
            });
        }

        public bool IsBlacklisted(string hwid)
        {
            var store = _repo.Load();
            return store.BlacklistedHwids.Contains(hwid);
        }

        public void BlacklistHwid(string hwid, string reason)
        {
            _repo.Update(store =>
            {
                store.BlacklistedHwids.Add(hwid);
                store.Incidents.Add(new Incident
                {
                    Severity = "High",
                    ViolationType = "PRELOGIN_BLOCK",
                    Message = reason,
                    Hwid = hwid,
                    PostLogin = false
                });
                return 0;
            });
            Logger.Warn($"HWID added to blacklist: {hwid}");
        }

        public void LockAccount(string serialKey, string productId, string hwid, string reason, bool postLogin)
        {
            _repo.Update(store =>
            {
                var k = store.SerialKeys.Find(k => string.Equals(k.Key, serialKey, StringComparison.OrdinalIgnoreCase)
                                                 && string.Equals(k.ProductId, productId, StringComparison.OrdinalIgnoreCase));
                if (k != null) k.Locked = true;
                store.Incidents.Add(new Incident
                {
                    Severity = "High",
                    ViolationType = "POSTLOGIN_LOCK",
                    Message = reason,
                    SerialKey = serialKey,
                    ProductId = productId,
                    Hwid = hwid,
                    PostLogin = postLogin
                });
                return 0;
            });
            Logger.Warn($"Account locked for key {serialKey}: {reason}");
        }

        // Admin operations
        public SerialKey CreateSerialKey(string productId, string productName, DateTime expirationUtc, int allowedHwidResets)
        {
            var key = new SerialKey
            {
                ProductId = productId,
                ProductName = productName,
                ExpirationUtc = expirationUtc,
                AllowedHwidResets = Math.Max(0, allowedHwidResets),
                Key = GenerateKeyString(productId)
            };
            _repo.Update(store =>
            {
                store.SerialKeys.Add(key);
                return 0;
            });
            Logger.Info($"Created serial key for {productId}: {key.Key}");
            return key;
        }

        public bool ResetHwid(string serialKey, string productId, out string message)
        {
            bool result = false;
            string tmpMessage = string.Empty;
            _repo.Update(store =>
            {
                var k = store.SerialKeys.Find(k => string.Equals(k.Key, serialKey, StringComparison.OrdinalIgnoreCase)
                                                 && string.Equals(k.ProductId, productId, StringComparison.OrdinalIgnoreCase));
                if (k == null)
                {
                    tmpMessage = "Key not found";
                    return 0;
                }
                if (k.AllowedHwidResets <= 0)
                {
                    tmpMessage = "No HWID resets remaining";
                    return 0;
                }
                k.BoundHwid = null;
                k.AllowedHwidResets -= 1;
                k.Locked = false; // unlock when resetting
                result = true;
                tmpMessage = "HWID reset successful";
                store.Incidents.Add(new Incident
                {
                    Severity = "Medium",
                    ViolationType = "HWID_RESET",
                    Message = "Admin HWID reset",
                    SerialKey = serialKey,
                    ProductId = productId,
                    PostLogin = false
                });
                return 0;
            });
            message = tmpMessage;
            return result;
        }

        public bool UnlockAccount(string serialKey, string productId)
        {
            bool changed = false;
            _repo.Update(store =>
            {
                var k = store.SerialKeys.Find(k => string.Equals(k.Key, serialKey, StringComparison.OrdinalIgnoreCase)
                                                 && string.Equals(k.ProductId, productId, StringComparison.OrdinalIgnoreCase));
                if (k != null && k.Locked)
                {
                    k.Locked = false;
                    changed = true;
                }
                return 0;
            });
            if (changed) Logger.Info($"Unlocked account for key {serialKey}");
            return changed;
        }

        public bool RemoveFromBlacklist(string hwid)
        {
            bool removed = false;
            _repo.Update(store =>
            {
                removed = store.BlacklistedHwids.Remove(hwid);
                if (removed)
                {
                    store.Incidents.Add(new Incident
                    {
                        Severity = "Low",
                        ViolationType = "BLACKLIST_REMOVE",
                        Message = "Admin removed HWID from blacklist",
                        Hwid = hwid
                    });
                }
                return 0;
            });
            if (removed) Logger.Info($"Removed HWID from blacklist: {hwid}");
            return removed;
        }

        public DataStore LoadStore() => _repo.Load();

        private static string GenerateKeyString(string productId)
        {
            // Simple RFC4122-based with product prefix
            var guid = Guid.NewGuid().ToString("N").ToUpperInvariant();
            var key = $"{productId}-{guid.Substring(0,8)}-{guid.Substring(8,8)}-{guid.Substring(16,8)}";
            return key;
        }
    }
}
