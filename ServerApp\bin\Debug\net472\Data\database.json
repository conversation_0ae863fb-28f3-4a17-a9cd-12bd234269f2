{"SerialKeys": [{"ProductId": "1", "ProductName": "test", "Key": "1-C68E7B5C-6F054ECB-AC24525C", "ExpirationUtc": "2026-09-01T11:39:24Z", "BoundHwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "Locked": true, "AllowedHwidResets": 13, "CreatedUtc": "2025-08-05T11:39:55.4563797Z", "LastUsedUtc": "2025-08-05T15:03:42.5588268Z"}], "Incidents": [{"Id": "39a23465a72846338b55527b3c108e21", "TimestampUtc": "2025-08-05T12:50:04.6115226Z", "Severity": "High", "ViolationType": "POSTLOGIN_LOCK", "Message": "Post-login violation: DEBUGGER_DETECTED", "SerialKey": "1-C68E7B5C-6F054ECB-AC24525C", "ProductId": "1", "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": true, "ContextJson": null}, {"Id": "6af3119e82344409b57906162010e70c", "TimestampUtc": "2025-08-05T12:50:03.4721722Z", "Severity": "High", "ViolationType": "DEBUGGER_DETECTED", "Message": "Debugger attached after login", "SerialKey": "1-C68E7B5C-6F054ECB-AC24525C", "ProductId": "1", "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": true, "ContextJson": null}, {"Id": "4f9c7466cdf045acb3ca3f54437daec1", "TimestampUtc": "2025-08-05T15:03:44.0903386Z", "Severity": "High", "ViolationType": "POSTLOGIN_LOCK", "Message": "Post-login violation: DEBUGGER_DETECTED", "SerialKey": "1-C68E7B5C-6F054ECB-AC24525C", "ProductId": "1", "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": true, "ContextJson": null}, {"Id": "0b4354c18f014e93829170355346a4f9", "TimestampUtc": "2025-08-05T15:03:43.2677501Z", "Severity": "High", "ViolationType": "DEBUGGER_DETECTED", "Message": "Debugger attached after login", "SerialKey": "1-C68E7B5C-6F054ECB-AC24525C", "ProductId": "1", "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": true, "ContextJson": null}, {"Id": "00f3b66093784182b56c07d2d11367df", "TimestampUtc": "2025-08-05T15:05:25.6198004Z", "Severity": "High", "ViolationType": "PRELOGIN_BLOCK", "Message": "Pre-login violation: DEBUGGER_DETECTED", "SerialKey": null, "ProductId": null, "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}, {"Id": "9d718ab0f2d1435e84749731b9aa9dfc", "TimestampUtc": "2025-08-05T15:05:24.7668254Z", "Severity": "High", "ViolationType": "DEBUGGER_DETECTED", "Message": "Debugger detected before login", "SerialKey": "", "ProductId": "", "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}, {"Id": "f57d2b656159457693a45374c57a8d60", "TimestampUtc": "2025-08-05T15:09:51.5462404Z", "Severity": "High", "ViolationType": "PRELOGIN_BLOCK", "Message": "Pre-login violation: DEBUGGER_DETECTED", "SerialKey": null, "ProductId": null, "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}, {"Id": "582078db85584bee8020116005fa2435", "TimestampUtc": "2025-08-05T15:09:50.4894098Z", "Severity": "High", "ViolationType": "DEBUGGER_DETECTED", "Message": "Debugger detected before login", "SerialKey": "", "ProductId": "", "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}, {"Id": "3a9e789e5d764b059f2a65c2a7d30f71", "TimestampUtc": "2025-08-05T15:10:31.4003655Z", "Severity": "Low", "ViolationType": "BLACKLIST_REMOVE", "Message": "<PERSON><PERSON> removed HWID from blacklist", "SerialKey": null, "ProductId": null, "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}, {"Id": "7f0778bfd1a8424db9bf598327d8d127", "TimestampUtc": "2025-08-05T15:10:47.1935535Z", "Severity": "High", "ViolationType": "PRELOGIN_BLOCK", "Message": "Pre-login violation: DEBUGGER_DETECTED", "SerialKey": null, "ProductId": null, "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}, {"Id": "c5a82400d2f940c0a694282e3d929896", "TimestampUtc": "2025-08-05T15:10:46.3352089Z", "Severity": "High", "ViolationType": "DEBUGGER_DETECTED", "Message": "Debugger detected before login", "SerialKey": "", "ProductId": "", "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}, {"Id": "4cd3727792a9498c964bf45f7ebefdad", "TimestampUtc": "2025-08-05T15:11:04.3983825Z", "Severity": "High", "ViolationType": "PRELOGIN_BLOCK", "Message": "Pre-login violation: DEBUGGER_DETECTED", "SerialKey": null, "ProductId": null, "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}, {"Id": "aa7cb20184934830800d0522e376232d", "TimestampUtc": "2025-08-05T15:11:03.5342149Z", "Severity": "High", "ViolationType": "DEBUGGER_DETECTED", "Message": "Debugger detected before login", "SerialKey": "", "ProductId": "", "Hwid": "C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939", "ClientInfo": null, "PostLogin": false, "ContextJson": null}], "BlacklistedHwids": ["C017ACA566924F20E47C5C1FB4CBD551D752C133D7CDB62885645A736B280939"]}