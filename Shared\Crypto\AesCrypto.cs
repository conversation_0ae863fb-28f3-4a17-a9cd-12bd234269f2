using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace SecureWinSuite.Shared.Crypto
{
    public static class AesCrypto
    {
        private const int IvSize = 16; // AES block size
        private const int KeySize = 32; // 256-bit
        private const int HmacSize = 32; // HMACSHA256 output

        public static byte[] Encrypt(byte[] plaintext, string passphrase, byte[] salt, int iterations)
        {
            if (plaintext == null) throw new ArgumentNullException(nameof(plaintext));
            if (salt == null) throw new ArgumentNullException(nameof(salt));

            using var rng = RandomNumberGenerator.Create();
            var iv = new byte[IvSize];
            rng.GetBytes(iv);

            DeriveKeys(passphrase, salt, iterations, out var encKey, out var macKey);

            byte[] ciphertext;
            using (var aes = Aes.Create())
            {
                aes.KeySize = 256;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                aes.IV = iv;
                aes.Key = encKey;
                using var ms = new MemoryStream();
                using var cs = new CryptoStream(ms, aes.CreateEncryptor(), CryptoStreamMode.Write);
                cs.Write(plaintext, 0, plaintext.Length);
                cs.FlushFinalBlock();
                ciphertext = ms.ToArray();
            }

            // Compute HMAC over (iv || ciphertext)
            var dataToMac = new byte[IvSize + ciphertext.Length];
            Buffer.BlockCopy(iv, 0, dataToMac, 0, IvSize);
            Buffer.BlockCopy(ciphertext, 0, dataToMac, IvSize, ciphertext.Length);
            byte[] hmac;
            using (var h = new HMACSHA256(macKey))
            {
                hmac = h.ComputeHash(dataToMac);
            }

            var result = new byte[IvSize + ciphertext.Length + HmacSize];
            Buffer.BlockCopy(iv, 0, result, 0, IvSize);
            Buffer.BlockCopy(ciphertext, 0, result, IvSize, ciphertext.Length);
            Buffer.BlockCopy(hmac, 0, result, IvSize + ciphertext.Length, HmacSize);
            return result;
        }

        public static byte[] Decrypt(byte[] data, string passphrase, byte[] salt, int iterations)
        {
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (data.Length < IvSize + HmacSize) throw new ArgumentException("Invalid ciphertext length");

            var iv = new byte[IvSize];
            Buffer.BlockCopy(data, 0, iv, 0, IvSize);
            var mac = new byte[HmacSize];
            Buffer.BlockCopy(data, data.Length - HmacSize, mac, 0, HmacSize);
            var ciphertextLen = data.Length - IvSize - HmacSize;
            var ciphertext = new byte[ciphertextLen];
            Buffer.BlockCopy(data, IvSize, ciphertext, 0, ciphertextLen);

            DeriveKeys(passphrase, salt, iterations, out var encKey, out var macKey);

            // Verify HMAC
            var dataToMac = new byte[IvSize + ciphertext.Length];
            Buffer.BlockCopy(iv, 0, dataToMac, 0, IvSize);
            Buffer.BlockCopy(ciphertext, 0, dataToMac, IvSize, ciphertext.Length);
            using (var h = new HMACSHA256(macKey))
            {
                var computed = h.ComputeHash(dataToMac);
                if (!FixedTimeEquals(mac, computed))
                    throw new CryptographicException("HMAC validation failed");
            }

            using var aes = Aes.Create();
            aes.KeySize = 256;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;
            aes.IV = iv;
            aes.Key = encKey;
            using var ms = new MemoryStream();
            using var cs = new CryptoStream(ms, aes.CreateDecryptor(), CryptoStreamMode.Write);
            cs.Write(ciphertext, 0, ciphertext.Length);
            cs.FlushFinalBlock();
            return ms.ToArray();
        }

        private static void DeriveKeys(string passphrase, byte[] salt, int iterations, out byte[] encKey, out byte[] macKey)
        {
            using var kdf = new Rfc2898DeriveBytes(passphrase, salt, iterations, HashAlgorithmName.SHA256);
            var keyMaterial = kdf.GetBytes(KeySize * 2);
            encKey = new byte[KeySize];
            macKey = new byte[KeySize];
            Buffer.BlockCopy(keyMaterial, 0, encKey, 0, KeySize);
            Buffer.BlockCopy(keyMaterial, KeySize, macKey, 0, KeySize);
        }

        public static byte[] EncryptString(string plaintext, string passphrase, byte[] salt, int iterations)
        {
            return Encrypt(Encoding.UTF8.GetBytes(plaintext), passphrase, salt, iterations);
        }

        public static string DecryptToString(byte[] data, string passphrase, byte[] salt, int iterations)
        {
            var bytes = Decrypt(data, passphrase, salt, iterations);
            return Encoding.UTF8.GetString(bytes);
        }

        private static bool FixedTimeEquals(byte[] a, byte[] b)
        {
            if (a == null || b == null) return false;
            if (a.Length != b.Length) return false;
            int diff = 0;
            for (int i = 0; i < a.Length; i++)
            {
                diff |= a[i] ^ b[i];
            }
            return diff == 0;
        }
    }
}
