using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using SecureWinSuite.ServerApp.Configuration;
using SecureWinSuite.ServerApp.Services;
using SecureWinSuite.Shared.Logging;
using SecureWinSuite.Shared.Networking;
using SecureWinSuite.Shared.Models;

namespace SecureWinSuite.ServerApp.Networking
{
    public class TcpServer
    {
        private TcpListener? _listener;
        private CancellationTokenSource? _cts;
        private readonly ConcurrentDictionary<int, ClientSession> _clients = new ConcurrentDictionary<int, ClientSession>();
        private int _nextId = 0;

        public event EventHandler<string>? StatusChanged;

        public void Start()
        {
            if (_listener != null) return;
            Logger.Init();
            var ip = IPAddress.Any;
            _listener = new TcpListener(ip, ServerConfig.Port);
            _cts = new CancellationTokenSource();
            _listener.Start();
            OnStatus($"Server started on port {ServerConfig.Port}");
            _ = AcceptLoopAsync(_cts.Token);
        }

        public void Stop()
        {
            try { _cts?.Cancel(); } catch { }
            try { _listener?.Stop(); } catch { }
            foreach (var kv in _clients)
            {
                kv.Value.Dispose();
            }
            _clients.Clear();
            _listener = null;
            OnStatus("Server stopped");
        }

        private async Task AcceptLoopAsync(CancellationToken ct)
        {
            if (_listener == null) return;
            while (!ct.IsCancellationRequested)
            {
                try
                {
                    var tcp = await _listener.AcceptTcpClientAsync().ConfigureAwait(false);
                    tcp.NoDelay = true;
                    var id = Interlocked.Increment(ref _nextId);
                    var session = new ClientSession(id, tcp, OnStatus);
                    if (_clients.TryAdd(id, session))
                    {
                        _ = session.RunAsync(ct).ContinueWith(t =>
                        {
                            _clients.TryRemove(id, out _);
                            if (t.Exception != null)
                                Logger.Error($"Session {id} error", t.Exception.InnerException ?? t.Exception);
                        });
                    }
                }
                catch (ObjectDisposedException) { break; }
                catch (Exception ex)
                {
                    if (!ct.IsCancellationRequested)
                    {
                        Logger.Error("AcceptLoop error", ex);
                        await Task.Delay(500, ct).ConfigureAwait(false);
                    }
                }
            }
        }

        private void OnStatus(string msg)
        {
            Logger.Info(msg);
            StatusChanged?.Invoke(this, msg);
        }

        private class ClientSession : IDisposable
        {
            private readonly int _id;
            private readonly TcpClient _tcp;
            private readonly AesTcpProtocol _protocol;
            private readonly Action<string> _status;
            private readonly LicenseService _license = new LicenseService();
            private string _hwid = string.Empty;
            private string _serialKey = string.Empty;
            private string _productId = string.Empty;
            private bool _authenticated;

            public ClientSession(int id, TcpClient tcp, Action<string> status)
            {
                _id = id;
                _tcp = tcp;
                _status = status;
                _protocol = new AesTcpProtocol(ServerConfig.Passphrase, ServerConfig.Salt, ServerConfig.Iterations);
            }

            public async Task RunAsync(CancellationToken serverCt)
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(serverCt);
                var ct = linkedCts.Token;
                try
                {
                    _status($"Client #{_id} connected from {(_tcp.Client.RemoteEndPoint?.ToString() ?? "unknown")}");
                    using var stream = _tcp.GetStream();
                    while (!ct.IsCancellationRequested)
                    {
                        Packet? packet;
                        try
                        {
                            packet = await _protocol.ReceiveAsync(stream, ct).ConfigureAwait(false);
                        }
                        catch (Exception ex)
                        {
                            Logger.Warn($"Client #{_id} disconnected: {ex.Message}");
                            break;
                        }
                        if (packet == null) continue;
                        await HandlePacketAsync(stream, packet, ct).ConfigureAwait(false);
                    }
                }
                finally
                {
                    try { _tcp.Close(); } catch { }
                    _status($"Client #{_id} closed");
                }
            }

            private async Task HandlePacketAsync(NetworkStream stream, Packet packet, CancellationToken ct)
            {
                switch (packet.Type)
                {
                    case PacketType.Heartbeat:
                        // Optionally log; no response necessary
                        break;
                    case PacketType.LoginRequest:
                        await HandleLoginAsync(stream, packet, ct).ConfigureAwait(false);
                        break;
                    case PacketType.ViolationPreLogin:
                        HandleViolation(packet, postLogin: false);
                        // Silent block: do not reveal detection; close session
                        Dispose();
                        break;
                    case PacketType.ViolationPostLogin:
                        HandleViolation(packet, postLogin: true);
                        // Lock account and terminate session
                        Dispose();
                        break;
                    case PacketType.BlacklistCheckRequest:
                        try
                        {
                            var req = packet.GetPayload<BlacklistCheckRequest>();
                            var hwid = req?.Hwid ?? string.Empty;
                            var resp = new BlacklistCheckResponse { Blacklisted = _license.IsBlacklisted(hwid) };
                            await _protocol.SendAsync(stream, PacketType.BlacklistCheckResponse, resp, ct).ConfigureAwait(false);
                        }
                        catch (Exception ex)
                        {
                            Logger.Warn($"BlacklistCheck handling error: {ex.Message}");
                        }
                        finally
                        {
                            Dispose();
                        }
                        break;
                }
            }

            private async Task HandleLoginAsync(NetworkStream stream, Packet packet, CancellationToken ct)
            {
                var req = packet.GetPayload<LoginRequest>();
                if (req == null) return;
                _hwid = req.Hwid ?? string.Empty;
                _serialKey = req.SerialKey ?? string.Empty;
                _productId = req.ProductId ?? string.Empty;

                if (_license.IsBlacklisted(_hwid))
                {
                    // Silently block: generic message, no details
                    var resp = new LoginResponse { Success = false, Message = "Access denied", Locked = false };
                    await _protocol.SendAsync(stream, PacketType.LoginResponse, resp, ct).ConfigureAwait(false);
                    Logger.Warn($"Blocked blacklisted HWID attempting login #{_id}");
                    Dispose();
                    return;
                }

                var (success, message, expiration, locked) = _license.Authenticate(_serialKey, _productId, _hwid);
                var lr = new LoginResponse
                {
                    Success = success,
                    Message = message,
                    ExpirationUtc = expiration,
                    Locked = locked
                };
                await _protocol.SendAsync(stream, PacketType.LoginResponse, lr, ct).ConfigureAwait(false);
                _authenticated = success;
                if (success)
                {
                    _status($"Client #{_id} authenticated: {_productId}");
                }
                else if (locked)
                {
                    // already locked by policy; just close
                    Dispose();
                }
            }

            private void HandleViolation(Packet packet, bool postLogin)
            {
                var report = packet.GetPayload<ViolationReport>();
                if (report == null) return;
                var incident = new Incident
                {
                    TimestampUtc = report.TimestampUtc,
                    Severity = "High",
                    ViolationType = report.ViolationType,
                    Message = report.Message,
                    SerialKey = report.SerialKey,
                    ProductId = report.ProductId,
                    Hwid = report.Hwid,
                    PostLogin = postLogin,
                    ContextJson = report.ContextJson
                };
                if (!postLogin)
                {
                    // Pre-login: blacklist HWID and log
                    _license.BlacklistHwid(report.Hwid, $"Pre-login violation: {report.ViolationType}");
                }
                else
                {
                    // Post-login: lock account and log
                    _license.LockAccount(report.SerialKey, report.ProductId, report.Hwid, $"Post-login violation: {report.ViolationType}", true);
                }
                _license.RecordIncident(incident);
                _status($"Violation recorded from client #{_id}: {report.ViolationType} (postLogin={postLogin})");
            }

            public void Dispose()
            {
                try { _tcp.Close(); } catch { }
            }
        }
    }
}
