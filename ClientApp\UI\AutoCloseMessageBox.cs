using System;
using System.Drawing;
using System.Windows.Forms;

namespace SecureWinSuite.ClientApp.UI
{
    public static class AutoCloseMessageBox
    {
        public static void Show(string text, string caption, int seconds, bool errorIcon)
        {
            using (var dlg = new Form())
            {
                dlg.Text = caption;
                dlg.FormBorderStyle = FormBorderStyle.FixedDialog;
                dlg.StartPosition = FormStartPosition.CenterScreen;
                dlg.ClientSize = new Size(420, 140);
                dlg.MinimizeBox = false;
                dlg.MaximizeBox = false;
                dlg.TopMost = true;
                dlg.ShowInTaskbar = false;

                var icon = errorIcon ? SystemIcons.Error : SystemIcons.Information;

                var pb = new PictureBox
                {
                    Image = icon.ToBitmap(),
                    SizeMode = PictureBoxSizeMode.CenterImage,
                    Size = new Size(48, 48),
                    Location = new Point(16, 24)
                };
                dlg.Controls.Add(pb);

                var lbl = new Label
                {
                    AutoSize = false,
                    Location = new Point(80, 16),
                    Size = new Size(320, 60),
                    Text = text
                };
                dlg.Controls.Add(lbl);

                var lblCountdown = new Label
                {
                    AutoSize = true,
                    Location = new Point(80, 82),
                    Text = $"Closing in {seconds} s..."
                };
                dlg.Controls.Add(lblCountdown);

                var timer = new Timer { Interval = 1000 };
                timer.Tick += (s, e) =>
                {
                    seconds--;
                    if (seconds <= 0)
                    {
                        timer.Stop();
                        dlg.Close();
                    }
                    else
                    {
                        lblCountdown.Text = $"Closing in {seconds} s...";
                    }
                };
                dlg.Shown += (s, e) => timer.Start();

                var btn = new Button
                {
                    Text = "OK",
                    DialogResult = DialogResult.OK,
                    Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                    Location = new Point(dlg.ClientSize.Width - 96, dlg.ClientSize.Height - 36),
                    Size = new Size(80, 24)
                };
                dlg.Controls.Add(btn);
                dlg.AcceptButton = btn;
                dlg.CancelButton = btn;

                dlg.ShowDialog();
            }
        }
    }
}
